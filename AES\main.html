<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具集合 - AES加密 & 字符串转换</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
        }

        /* 左侧导航栏 */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 30px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .sidebar-header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .nav-menu {
            flex: 1;
            padding: 20px 0;
        }

        .nav-item {
            display: block;
            width: 100%;
            padding: 18px 25px;
            border: none;
            background: none;
            text-align: left;
            font-size: 16px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-item:hover {
            background: #f8f9fa;
            border-left-color: #667eea;
        }

        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-left-color: #fff;
        }

        .nav-item .icon {
            font-size: 20px;
            width: 24px;
            text-align: center;
        }

        .nav-item .text {
            flex: 1;
        }

        .nav-item .description {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 2px;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .tool-container {
            display: none;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            min-height: calc(100vh - 40px);
        }

        .tool-container.active {
            display: block;
        }

        /* 移动端适配 */
        .mobile-toggle {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 999;
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                width: 100%;
                padding: 70px 10px 10px 10px;
            }

            .mobile-toggle {
                display: block;
            }

            .tool-container {
                min-height: calc(100vh - 80px);
            }
        }

        /* 遮罩层 */
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 998;
        }

        .overlay.show {
            display: block;
        }
    </style>
</head>
<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-toggle" onclick="toggleSidebar()">
        <span style="font-size: 20px;">☰</span>
    </button>

    <!-- 遮罩层 -->
    <div class="overlay" onclick="closeSidebar()"></div>

    <!-- 左侧导航栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h1>工具集合</h1>
            <div class="subtitle">by lyg</div>
        </div>
        
        <nav class="nav-menu">
            <button class="nav-item active" onclick="showTool('aes')">
                <span class="icon">🔒</span>
                <div class="text">
                    <div>AES 加解密</div>
                    <div class="description">文本加密解密工具</div>
                </div>
            </button>
            
            <button class="nav-item" onclick="showTool('converter')">
                <span class="icon">🔄</span>
                <div class="text">
                    <div>字符串转换</div>
                    <div class="description">多行转逗号分隔</div>
                </div>
            </button>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- AES 加解密工具 -->
        <div id="aes-tool" class="tool-container active">
            <iframe src="index.html" style="width: 100%; height: 100%; border: none; border-radius: 15px;"></iframe>
        </div>

        <!-- 字符串转换工具 -->
        <div id="converter-tool" class="tool-container">
            <iframe src="fwgszh.html" style="width: 100%; height: 100%; border: none; border-radius: 15px;"></iframe>
        </div>
    </div>

    <script>
        // 显示指定工具
        function showTool(toolName) {
            // 隐藏所有工具容器
            const containers = document.querySelectorAll('.tool-container');
            containers.forEach(container => {
                container.classList.remove('active');
            });

            // 移除所有导航项的激活状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的工具
            const targetContainer = document.getElementById(toolName + '-tool');
            if (targetContainer) {
                targetContainer.classList.add('active');
            }

            // 激活对应的导航项
            const activeNavItem = document.querySelector(`[onclick="showTool('${toolName}')"]`);
            if (activeNavItem) {
                activeNavItem.classList.add('active');
            }

            // 在移动端关闭侧边栏
            if (window.innerWidth <= 768) {
                closeSidebar();
            }
        }

        // 切换侧边栏显示/隐藏
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.overlay');
            
            sidebar.classList.toggle('open');
            overlay.classList.toggle('show');
        }

        // 关闭侧边栏
        function closeSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.overlay');
            
            sidebar.classList.remove('open');
            overlay.classList.remove('show');
        }

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeSidebar();
            }
        });

        // 阻止iframe内的点击事件冒泡到父页面
        document.addEventListener('DOMContentLoaded', function() {
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(iframe => {
                iframe.addEventListener('load', function() {
                    try {
                        // 尝试为iframe内容添加点击事件监听（如果同源）
                        iframe.contentWindow.addEventListener('click', function(e) {
                            e.stopPropagation();
                        });
                    } catch (e) {
                        // 跨域情况下忽略错误
                        console.log('Cross-origin iframe detected');
                    }
                });
            });
        });
    </script>
</body>
</html>
