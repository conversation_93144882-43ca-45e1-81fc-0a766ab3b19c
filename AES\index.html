<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> AES 加解密系统 </title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #667eea;
            border-radius: 10px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .header h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #666;
            font-size: 14px;
        }

        .mode-selector {
            margin-bottom: 30px;
        }

        .mode-buttons {
            display: flex;
            gap: 10px;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .mode-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            background: transparent;
            color: #6c757d;
        }

        .mode-btn.active {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .encrypt-btn.active {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .decrypt-btn.active {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
        }

        .mode-btn:hover {
            background: #e9ecef;
        }

        .mode-btn.active:hover {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .decrypt-btn.active:hover {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        .operation-panel {
            display: none;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .operation-panel.active {
            display: block;
        }

        .operation-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }

        .input-container {
            position: relative;
        }

        .input-group textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            padding-right: 50px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .copy-input-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 6px 10px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .copy-input-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .copy-input-btn:active {
            transform: translateY(0);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .process-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .clear-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .back-btn {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .result-panel {
            display: none;
            background: white;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .result-panel.success {
            border-color: #28a745;
            background: #f8fff9;
        }

        .result-panel.error {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .result-title {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-title.success {
            color: #28a745;
        }

        .result-title.error {
            color: #dc3545;
        }

        .result-content {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .copy-btn {
            margin-top: 10px;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #5a6fd8;
        }

        .link-generation {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .link-generation.show {
            display: block;
        }

        .link-generation h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .link-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .link-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }

        .pc-link-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .mobile-link-btn {
            background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
        }

        .link-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .link-result {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: white;
            border: 2px solid #28a745;
            border-radius: 8px;
        }

        .link-result.show {
            display: block;
        }

        .link-result.pc {
            border-color: #28a745;
            background: #f8fff9;
        }

        .link-result.mobile {
            border-color: #fd7e14;
            background: #fff8f0;
        }

        .link-title {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .link-title.pc {
            color: #28a745;
        }

        .link-title.mobile {
            color: #fd7e14;
        }

        .link-content {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            white-space: pre-wrap;
            max-height: 150px;
            overflow-y: auto;
            font-size: 12px;
        }

        .link-copy-btn {
            margin-top: 10px;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .link-copy-btn:hover {
            background: #5a6fd8;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 20px;
            }

            .mode-buttons {
                flex-direction: column;
                gap: 5px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .mode-btn {
                padding: 12px 15px;
                font-size: 14px;
            }

            .copy-input-btn {
                top: 6px;
                right: 6px;
                padding: 4px 8px;
                font-size: 11px;
            }

            .input-group textarea {
                padding-right: 45px;
            }

            .link-buttons {
                flex-direction: column;
                gap: 8px;
            }

            .link-btn {
                padding: 10px;
                font-size: 13px;
            }

            .link-content {
                font-size: 11px;
                max-height: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 欢迎界面 -->
        <div class="header">
            <h1>AES 加解密系统</h1>
            <div class="subtitle">by lyg</div>
        </div>

        <!-- 操作模式选择 -->
        <div class="mode-selector">
            <div class="mode-buttons">
                <button id="encryptModeBtn" class="mode-btn encrypt-btn active" onclick="setMode('encrypt')">
                    🔒 加密模式
                </button>
                <button id="decryptModeBtn" class="mode-btn decrypt-btn" onclick="setMode('decrypt')">
                    🔓 解密模式
                </button>
            </div>
        </div>

        <!-- 统一操作面板 -->
        <div id="operationPanel" class="operation-panel active">
            <div id="operationTitle" class="operation-title">� 文本加密</div>
            <div class="input-group">
                <label id="inputLabel" for="inputText">请输入要加密的文本:</label>
                <div class="input-container">
                    <textarea id="inputText" placeholder="在此输入需要处理的文本内容..."></textarea>
                    <!-- <button class="copy-input-btn" onclick="copyInputText()" title="复制输入内容">复制</button> -->
                </div>
            </div>
            <div class="action-buttons">
                <button id="processBtn" class="action-btn process-btn" onclick="processText()">加密</button>
                <button class="action-btn clear-btn" onclick="clearInput()">清空</button>
            </div>
            <div id="result" class="result-panel">
                <div class="result-title">
                    <span id="resultIcon">✅</span>
                    <span id="resultTitle">处理结果</span>
                </div>
                <div id="resultContent" class="result-content"></div>
                <button class="copy-btn" onclick="copyResult('resultContent')">复制结果</button>
            </div>

            <!-- 链接生成区域 -->
            <div id="linkGeneration" class="link-generation">
                <h3>🔗 生成登录链接</h3>
                <div class="link-buttons">
                    <button class="link-btn pc-link-btn" onclick="generatePCLink()">
                        💻 生成PC链接
                    </button>
                    <button class="link-btn mobile-link-btn" onclick="generateMobileLink()">
                        📱 生成移动端链接
                    </button>
                </div>

                <!-- PC链接结果 -->
                <div id="pcLinkResult" class="link-result pc">
                    <div class="link-title pc">
                        <span>💻</span>
                        <span>PC端登录链接</span>
                    </div>
                    <div id="pcLinkContent" class="link-content"></div>
                    <button class="link-copy-btn" onclick="copyLink('pcLinkContent')">复制PC链接</button>
                </div>

                <!-- 移动端链接结果 -->
                <div id="mobileLinkResult" class="link-result mobile">
                    <div class="link-title mobile">
                        <span>📱</span>
                        <span>移动端登录链接</span>
                    </div>
                    <div id="mobileLinkContent" class="link-content"></div>
                    <button class="link-copy-btn" onclick="copyLink('mobileLinkContent')">复制移动端链接</button>
                </div>
            </div>
        </div>
    </div>

    <script src="./crypto-js.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
